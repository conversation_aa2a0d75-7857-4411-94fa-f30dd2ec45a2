<template>
  <div class="sub-time-config">
    <!-- 峰平谷时段配置对话框 -->
    <el-dialog 
      title="峰平谷时段配置" 
      :visible.sync="visible" 
      width="80%" 
      :before-close="handleClose"
      @close="handleClose"
    >
      <div class="time-config-container">
        <!-- 操作按钮 -->
        <div class="config-toolbar">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="addTimeConfig">新增配置</el-button>
          <el-button type="success" icon="el-icon-check" size="small" @click="saveTimeConfig">保存配置</el-button>
        </div>

        <!-- 配置表格 -->
        <el-table :data="timeConfigList" border style="width: 100%; margin-top: 15px;" v-loading="timeConfigLoading">
          <el-table-column prop="energyType" label="能源类型" width="120">
            <template slot-scope="scope">
              <el-select v-model="scope.row.energyType" placeholder="请选择" size="small" style="width: 100%">
                <el-option
                  v-for="option in dict.type.energy_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="峰平谷类型" width="120">
            <template slot-scope="scope">
              <el-select v-model="scope.row.type" placeholder="请选择" size="small" style="width: 100%">
                <el-option label="峰时段" value="peak"></el-option>
                <el-option label="平时段" value="flat"></el-option>
                <el-option label="谷时段" value="low"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开始时间" width="120">
            <template slot-scope="scope">
              <el-time-picker
                v-model="scope.row.startTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="开始时间"
                size="small"
                style="width: 100%">
              </el-time-picker>
            </template>
          </el-table-column>
          <el-table-column prop="endTime" label="结束时间" width="120">
            <template slot-scope="scope">
              <el-time-picker
                v-model="scope.row.endTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="结束时间"
                size="small"
                style="width: 100%">
              </el-time-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="deleteTimeConfig(scope.$index)" style="color: #f56c6c;">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listSubTimeConfig, addSubTimeConfig, updateSubTimeConfig, delSubTimeConfig} from "@/api/biz/subTimeConfig";

export default {
  name: 'SubTimeConfig',
  dicts: ['energy_type'],
  props: {
    // 控制对话框显示隐藏
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 时段配置相关
      timeConfigLoading: false,
      timeConfigList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.getTimeConfigList()
      } else {
        this.timeConfigList = []
      }
    }
  },
  methods: {
    // 获取时段配置列表
    getTimeConfigList() {
      this.timeConfigLoading = true
      listSubTimeConfig().then(response => {
        if (response.code === 200) {
          this.timeConfigList = response.rows || []
        }
        this.timeConfigLoading = false
      }).catch(error => {
        console.error('获取时段配置失败:', error)
        this.$message.error('获取时段配置失败')
        this.timeConfigLoading = false
      })
    },

    // 新增时段配置
    addTimeConfig() {
      this.timeConfigList.push({
        id: null,
        energyType: '',
        startTime: '',
        endTime: '',
        type: ''
      })
    },

    // 删除时段配置
    deleteTimeConfig(index) {
      const config = this.timeConfigList[index]
      if (config.id) {
        // 如果有ID，需要调用删除接口
        this.$confirm('确认删除该时段配置吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delSubTimeConfig(config.id).then(response => {
            if (response.code === 200) {
              this.$message.success('删除成功')
              this.timeConfigList.splice(index, 1)
            }
          }).catch(error => {
            console.error('删除失败:', error)
            this.$message.error('删除失败')
          })
        })
      } else {
        // 新增的未保存项，直接删除
        this.timeConfigList.splice(index, 1)
      }
    },

    // 保存时段配置
    saveTimeConfig() {
      // 验证数据
      for (let i = 0; i < this.timeConfigList.length; i++) {
        const config = this.timeConfigList[i]
        if (!config.energyType || !config.type || !config.startTime || !config.endTime) {
          this.$message.warning(`第${i + 1}行数据不完整，请检查`)
          return
        }
      }

      this.timeConfigLoading = true
      const promises = []

      this.timeConfigList.forEach(config => {
        if (config.id) {
          // 更新现有配置
          promises.push(updateSubTimeConfig(config))
        } else {
          // 新增配置
          promises.push(addSubTimeConfig(config))
        }
      })

      Promise.all(promises).then(responses => {
        const allSuccess = responses.every(response => response.code === 200)
        if (allSuccess) {
          this.$message.success('保存成功')
          this.getTimeConfigList() // 重新获取列表
          this.$emit('save-success') // 触发保存成功事件
        } else {
          this.$message.error('部分配置保存失败')
        }
        this.timeConfigLoading = false
      }).catch(error => {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
        this.timeConfigLoading = false
      })
    },

    // 关闭对话框
    handleClose() {
      this.visible = false
      this.timeConfigList = []
    }
  }
}
</script>

<style scoped>
/* 时段配置对话框样式 */
.time-config-container {
  padding: 10px;
}

.config-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.config-toolbar .el-button {
  margin-left: 0;
}

/* 表格内的选择器和时间选择器样式 */
.time-config-container .el-table .el-select,
.time-config-container .el-table .el-time-picker {
  width: 100%;
}

.time-config-container .el-table .el-input__inner {
  padding: 0 8px;
}
</style>
