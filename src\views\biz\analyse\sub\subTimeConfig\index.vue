<template>
  <div class="sub-time-config">
    <!-- 峰平谷时段配置对话框 -->
    <el-dialog
      title="峰平谷时段配置"
      :visible.sync="visible"
      width="800px"
      :before-close="handleClose"
      @close="handleClose"
      append-to-body
    >
      <div class="time-config-container">
        <!-- 操作按钮和提示 -->
        <div class="config-toolbar">
          <el-button type="primary" size="mini" plain @click="addTimeConfig">添加时段</el-button>
          <div style="margin-left: 20px; color: #909399; font-size: 12px;">
            <i class="el-icon-info"></i> 提示：时段必须连续设置，一天24小时需要完整覆盖，先完成当前时段才能添加下一个时段
          </div>
        </div>

        <!-- 时段配置表格 -->
        <el-table
          :data="timeConfigList"
          style="margin-top: 15px; width: 100%;"
          :max-height="400"
          border
          v-loading="timeConfigLoading"
        >
          <el-table-column label="开始时间" align="center" prop="startTime" min-width="30%">
            <template slot-scope="scope">
              <el-time-picker
                v-model="scope.row.startTime"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                placeholder="开始时间"
                style="width: 100%"
                :disabled="scope.$index > 0"
              >
              </el-time-picker>
            </template>
          </el-table-column>
          <el-table-column label="结束时间" align="center" prop="endTime" min-width="30%">
            <template slot-scope="scope">
              <el-time-picker
                v-model="scope.row.endTime"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                placeholder="结束时间"
                style="width: 100%"
                @change="handleEndTimeChange(scope.$index)"
              >
              </el-time-picker>
            </template>
          </el-table-column>
          <el-table-column label="峰平谷类型" align="center" prop="type" min-width="25%">
            <template slot-scope="scope">
              <el-select v-model="scope.row.type" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="option in dict.type.peak_valley_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" min-width="15%">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                @click="removeTimeConfig(scope.$index)"
              >
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listSubTimeConfig, updateSubTimeConfig} from "@/api/biz/subTimeConfig";

export default {
  name: 'SubTimeConfig',
  dicts: ['peak_valley_type'],
  props: {
    // 控制对话框显示隐藏
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 按钮loading
      buttonLoading: false,
      // 时段配置相关
      timeConfigLoading: false,
      // 时段配置列表
      timeConfigList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.reset()
        this.getTimeConfigList()
      }
    }
  },
  methods: {
    // 重置数据
    reset() {
      this.timeConfigList = []
    },

    // 取消按钮
    cancel() {
      this.visible = false
      this.reset()
    },

    // 获取时段配置列表
    getTimeConfigList() {
      this.timeConfigLoading = true
      listSubTimeConfig().then(response => {
        if (response.code === 200) {
          this.timeConfigList = response.data || []
        }
        this.timeConfigLoading = false
      }).catch(error => {
        console.error('获取时段配置失败:', error)
        this.$message.error('获取时段配置失败')
        this.timeConfigLoading = false
      })
    },

    // 新增时段配置
    addTimeConfig() {
      // 检查是否有未完成的配置
      if (this.timeConfigList.length > 0) {
        const lastConfig = this.timeConfigList[this.timeConfigList.length - 1];

        // 检查最后一项是否已完成
        if (!lastConfig.startTime ||
          !lastConfig.endTime ||
          !lastConfig.type) {
          this.$message.warning("请先完成当前时段的配置");
          return;
        }
      }

      let startTime = '00:00:00';
      if (this.timeConfigList.length > 0) {
        // 新时段的开始时间等于上一个时段的结束时间
        const lastConfig = this.timeConfigList[this.timeConfigList.length - 1];
        startTime = lastConfig.endTime;
      }

      this.timeConfigList.push({
        id: null,
        startTime: startTime,
        endTime: '',
        type: ''
      })
    },

    // 删除时段配置
    removeTimeConfig(index) {
      this.timeConfigList.splice(index, 1)
      // 重新调整后续时段的开始时间
      this.adjustTimeSequence(index)
    },

    // 处理结束时间变化
    handleEndTimeChange(index) {
      // 如果不是最后一个时段，更新下一个时段的开始时间
      if (index < this.timeConfigList.length - 1) {
        const currentConfig = this.timeConfigList[index];
        const nextConfig = this.timeConfigList[index + 1];
        if (currentConfig.endTime) {
          nextConfig.startTime = currentConfig.endTime;
        }
      }
    },

    // 调整时间序列
    adjustTimeSequence(fromIndex) {
      for (let i = fromIndex; i < this.timeConfigList.length; i++) {
        if (i === 0) {
          this.timeConfigList[i].startTime = '00:00:00';
        } else {
          const prevConfig = this.timeConfigList[i - 1];
          if (prevConfig.endTime) {
            this.timeConfigList[i].startTime = prevConfig.endTime;
          }
        }
      }
    },

    // 验证时段配置
    validateTimeConfig() {
      // 检查时段配置
      if (this.timeConfigList.length === 0) {
        this.$message.warning('请至少添加一个时段配置')
        return false
      }

      // 验证每个时段的完整性
      for (let i = 0; i < this.timeConfigList.length; i++) {
        const config = this.timeConfigList[i]
        if (!config.startTime || !config.endTime || !config.type) {
          this.$message.warning(`第${i + 1}个时段配置不完整，请检查`)
          return false
        }
      }

      // 验证时间连续性和覆盖24小时
      if (!this.validateTimeContinuity()) {
        return false
      }

      return true
    },

    // 验证时间连续性
    validateTimeContinuity() {
      if (this.timeConfigList.length === 0) return true

      // 排序时段配置
      const sortedConfigs = [...this.timeConfigList].sort((a, b) => {
        return a.startTime.localeCompare(b.startTime)
      })

      // 检查第一个时段是否从00:00:00开始
      if (sortedConfigs[0].startTime !== '00:00:00') {
        this.$message.warning('时段配置必须从00:00:00开始')
        return false
      }

      // 检查时段连续性
      for (let i = 0; i < sortedConfigs.length - 1; i++) {
        if (sortedConfigs[i].endTime !== sortedConfigs[i + 1].startTime) {
          this.$message.warning('时段配置必须连续，不能有间隙')
          return false
        }
      }

      // 检查最后一个时段是否到24:00:00
      const lastConfig = sortedConfigs[sortedConfigs.length - 1]
      if (lastConfig.endTime !== '24:00:00' && lastConfig.endTime !== '00:00:00') {
        this.$message.warning('时段配置必须覆盖完整的24小时')
        return false
      }

      return true
    },

    // 提交表单
    submitForm() {
      if (!this.validateTimeConfig()) return

      this.buttonLoading = true

      // 构造保存数据 - 封装为一个对象
      const saveData = {
        // 项目id
        projectId: localStorage.getItem('projectId'),
        // 时间段配置列表
        timeConfigs: this.timeConfigList.map(config => ({
          startTime: config.startTime,
          endTime: config.endTime,
          type: config.type
        }))
      }

      // 统一调用updateSubTimeConfig方法
      updateSubTimeConfig(saveData).then(response => {
        if (response.code === 200) {
          this.$message.success('保存成功')
          this.visible = false
          this.$emit('save-success', saveData)
        } else {
          this.$message.error('保存失败')
        }
        this.buttonLoading = false
      }).catch(error => {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
        this.buttonLoading = false
      })
    },

    // 关闭对话框
    handleClose() {
      this.visible = false
      this.reset()
    }
  }
}
</script>

<style scoped>
.sub-time-config {
  /* 主容器样式 */
}

/* 时段配置容器 */
.time-config-container {
  padding: 10px;
}

/* 操作工具栏 */
.config-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.config-toolbar .el-button {
  margin-left: 0;
}

/* 表格样式 */
.el-table {
  margin-top: 15px;
}

.el-table .el-input,
.el-table .el-select,
.el-table .el-time-picker {
  width: 100%;
}

.el-table .el-input__inner {
  border: none;
  padding: 0 8px;
  text-align: center;
}

.el-table .el-input__inner:focus {
  border: 1px solid #409EFF;
}

/* 提示信息样式 */
.el-icon-info {
  margin-right: 5px;
  color: #909399;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  text-align: right;
  padding: 20px 0 0 0;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
  }
}
</style>
