<template>
  <div class="sub-time-config">
    <!-- 峰平谷时段配置对话框 -->
    <el-dialog
      title="峰平谷时段配置"
      :visible.sync="visible"
      width="900px"
      :before-close="handleClose"
      @close="handleClose"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 100%;">
        <el-tabs v-model="activeTab" style="height: 500px">
          <!-- 基本信息Tab -->
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="配置名称" prop="configName">
              <el-input v-model="form.configName" placeholder="请输入配置名称" style="width: 100%"/>
            </el-form-item>
            <el-form-item label="能源类型" prop="energyType">
              <el-select
                v-model="form.energyType"
                placeholder="请选择能源类型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in dict.type.energy_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="3"
                placeholder="请输入配置描述"
                style="width: 100%"
              />
            </el-form-item>
          </el-tab-pane>

          <!-- 时段配置Tab -->
          <el-tab-pane label="时段配置" name="timeConfig">
            <el-button type="primary" size="mini" plain @click="addTimeConfig">添加时段</el-button>
            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
              <i class="el-icon-info"></i> 提示：时段必须连续设置，一天24小时需要完整覆盖，先完成当前时段才能添加下一个时段
            </div>
            <el-table
              :data="form.timeConfigList"
              style="margin-top: 10px; width: 100%;"
              :max-height="400"
              border
              v-loading="timeConfigLoading"
            >
              <el-table-column label="开始时间" align="center" prop="startTime" min-width="25%">
                <template slot-scope="scope">
                  <el-time-picker
                    v-model="scope.row.startTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="开始时间"
                    style="width: 100%"
                    :disabled="scope.$index > 0"
                  >
                  </el-time-picker>
                </template>
              </el-table-column>
              <el-table-column label="结束时间" align="center" prop="endTime" min-width="25%">
                <template slot-scope="scope">
                  <el-time-picker
                    v-model="scope.row.endTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="结束时间"
                    style="width: 100%"
                    @change="handleEndTimeChange(scope.$index)"
                  >
                  </el-time-picker>
                </template>
              </el-table-column>
              <el-table-column label="峰平谷类型" align="center" prop="type" min-width="25%">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.type" placeholder="请选择" style="width: 100%">
                    <el-option label="峰时段" value="peak"></el-option>
                    <el-option label="平时段" value="flat"></el-option>
                    <el-option label="谷时段" value="low"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="15%">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="removeTimeConfig(scope.$index)"
                  >
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listSubTimeConfig, addSubTimeConfig, updateSubTimeConfig, delSubTimeConfig} from "@/api/biz/subTimeConfig";

export default {
  name: 'SubTimeConfig',
  dicts: ['energy_type'],
  props: {
    // 控制对话框显示隐藏
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 时段配置相关
      timeConfigLoading: false,
      // 活动选项卡
      activeTab: 'basic',
      // 表单参数
      form: {
        id: undefined,
        configName: undefined,
        energyType: undefined,
        description: undefined,
        timeConfigList: []
      },
      // 表单校验
      rules: {
        configName: [
          { required: true, message: '配置名称不能为空', trigger: 'blur' }
        ],
        energyType: [
          { required: true, message: '能源类型不能为空', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.reset()
        this.getTimeConfigList()
      }
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        configName: undefined,
        energyType: undefined,
        description: undefined,
        timeConfigList: []
      }
      this.resetForm('form')
      this.activeTab = 'basic'
    },

    // 取消按钮
    cancel() {
      this.visible = false
      this.reset()
    },

    // 获取时段配置列表
    getTimeConfigList() {
      this.timeConfigLoading = true
      listSubTimeConfig().then(response => {
        if (response.code === 200) {
          // 这里可以根据需要处理现有配置的加载
          // 暂时保持为空，让用户创建新配置
        }
        this.timeConfigLoading = false
      }).catch(error => {
        console.error('获取时段配置失败:', error)
        this.$message.error('获取时段配置失败')
        this.timeConfigLoading = false
      })
    },

    // 新增时段配置
    addTimeConfig() {
      // 检查是否有未完成的配置
      if (this.form.timeConfigList.length > 0) {
        const lastConfig = this.form.timeConfigList[this.form.timeConfigList.length - 1];

        // 检查最后一项是否已完成
        if (!lastConfig.startTime ||
            !lastConfig.endTime ||
            !lastConfig.type) {
          this.$message.warning("请先完成当前时段的配置");
          return;
        }
      }

      let startTime = '00:00';
      if (this.form.timeConfigList.length > 0) {
        // 新时段的开始时间等于上一个时段的结束时间
        const lastConfig = this.form.timeConfigList[this.form.timeConfigList.length - 1];
        startTime = lastConfig.endTime;
      }

      this.form.timeConfigList.push({
        id: null,
        startTime: startTime,
        endTime: '',
        type: ''
      })
    },

    // 删除时段配置
    removeTimeConfig(index) {
      this.form.timeConfigList.splice(index, 1)
      // 重新调整后续时段的开始时间
      this.adjustTimeSequence(index)
    },

    // 处理结束时间变化
    handleEndTimeChange(index) {
      // 如果不是最后一个时段，更新下一个时段的开始时间
      if (index < this.form.timeConfigList.length - 1) {
        const currentConfig = this.form.timeConfigList[index];
        const nextConfig = this.form.timeConfigList[index + 1];
        if (currentConfig.endTime) {
          nextConfig.startTime = currentConfig.endTime;
        }
      }
    },

    // 调整时间序列
    adjustTimeSequence(fromIndex) {
      for (let i = fromIndex; i < this.form.timeConfigList.length; i++) {
        if (i === 0) {
          this.form.timeConfigList[i].startTime = '00:00';
        } else {
          const prevConfig = this.form.timeConfigList[i - 1];
          if (prevConfig.endTime) {
            this.form.timeConfigList[i].startTime = prevConfig.endTime;
          }
        }
      }
    },

    // 验证时段配置
    validateTimeConfig() {
      // 检查基本信息
      if (!this.form.configName) {
        this.$message.warning('请输入配置名称')
        this.activeTab = 'basic'
        return false
      }
      if (!this.form.energyType) {
        this.$message.warning('请选择能源类型')
        this.activeTab = 'basic'
        return false
      }

      // 检查时段配置
      if (this.form.timeConfigList.length === 0) {
        this.$message.warning('请至少添加一个时段配置')
        this.activeTab = 'timeConfig'
        return false
      }

      // 验证每个时段的完整性
      for (let i = 0; i < this.form.timeConfigList.length; i++) {
        const config = this.form.timeConfigList[i]
        if (!config.startTime || !config.endTime || !config.type) {
          this.$message.warning(`第${i + 1}个时段配置不完整，请检查`)
          this.activeTab = 'timeConfig'
          return false
        }
      }

      // 验证时间连续性和覆盖24小时
      if (!this.validateTimeContinuity()) {
        return false
      }

      return true
    },

    // 验证时间连续性
    validateTimeContinuity() {
      if (this.form.timeConfigList.length === 0) return true

      // 排序时段配置
      const sortedConfigs = [...this.form.timeConfigList].sort((a, b) => {
        return a.startTime.localeCompare(b.startTime)
      })

      // 检查第一个时段是否从00:00开始
      if (sortedConfigs[0].startTime !== '00:00') {
        this.$message.warning('时段配置必须从00:00开始')
        this.activeTab = 'timeConfig'
        return false
      }

      // 检查时段连续性
      for (let i = 0; i < sortedConfigs.length - 1; i++) {
        if (sortedConfigs[i].endTime !== sortedConfigs[i + 1].startTime) {
          this.$message.warning('时段配置必须连续，不能有间隙')
          this.activeTab = 'timeConfig'
          return false
        }
      }

      // 检查最后一个时段是否到24:00
      const lastConfig = sortedConfigs[sortedConfigs.length - 1]
      if (lastConfig.endTime !== '24:00' && lastConfig.endTime !== '00:00') {
        this.$message.warning('时段配置必须覆盖完整的24小时')
        this.activeTab = 'timeConfig'
        return false
      }

      return true
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return

        if (!this.validateTimeConfig()) return

        this.buttonLoading = true

        // 构造保存数据
        const saveData = {
          configName: this.form.configName,
          energyType: this.form.energyType,
          description: this.form.description,
          timeConfigs: this.form.timeConfigList.map(config => ({
            startTime: config.startTime,
            endTime: config.endTime,
            type: config.type
          }))
        }

        // 这里可以调用保存接口
        // 暂时模拟保存成功
        setTimeout(() => {
          this.$message.success('保存成功')
          this.buttonLoading = false
          this.visible = false
          this.$emit('save-success', saveData)
        }, 1000)
      })
    },

    // 关闭对话框
    handleClose() {
      this.visible = false
      this.reset()
    }
  }
}
</script>

<style scoped>
.sub-time-config {
  /* 主容器样式 */
}

/* 对话框内容样式 */
.el-dialog__body {
  padding: 20px;
}

/* Tab页面样式 */
.el-tabs {
  margin-top: 10px;
}

.el-tab-pane {
  padding: 20px 0;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 表格样式 */
.el-table {
  margin-top: 15px;
}

.el-table .el-input,
.el-table .el-select,
.el-table .el-time-picker {
  width: 100%;
}

.el-table .el-input__inner {
  border: none;
  padding: 0 8px;
  text-align: center;
}

.el-table .el-input__inner:focus {
  border: 1px solid #409EFF;
}

/* 按钮样式 */
.el-button + .el-button {
  margin-left: 10px;
}

/* 提示信息样式 */
.el-icon-info {
  margin-right: 5px;
  color: #909399;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  text-align: right;
  padding: 20px 0 0 0;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
  }

  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
