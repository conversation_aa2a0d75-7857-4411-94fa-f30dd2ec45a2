import request from '@/utils/request'

// 查询分段时间配置列表
export function listSubTimeConfig(query) {
  return request({
    url: '/biz/subTimeConfig/list',
    method: 'get',
    params: query
  })
}

// 查询分段时间配置详细
export function getSubTimeConfig(id) {
  return request({
    url: '/biz/subTimeConfig/' + id,
    method: 'get'
  })
}

// 新增分段时间配置
export function addSubTimeConfig(data) {
  return request({
    url: '/biz/subTimeConfig',
    method: 'post',
    data: data
  })
}

// 修改分段时间配置
export function updateSubTimeConfig(data) {
  return request({
    url: '/biz/subTimeConfig',
    method: 'put',
    data: data
  })
}

// 删除分段时间配置
export function delSubTimeConfig(id) {
  return request({
    url: '/biz/subTimeConfig/' + id,
    method: 'delete'
  })
}
